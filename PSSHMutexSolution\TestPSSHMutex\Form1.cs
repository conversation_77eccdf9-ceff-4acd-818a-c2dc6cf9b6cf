﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using PSSHConnMutex;

namespace TestPSSHMutex
{
    public partial class Form1 : Form
    {
        public List<SubAuthentication> subAuthenticationList = null;
        public string shellPrompt = "\\$|#|>";
        public Form1()
        {
            InitializeComponent();
        }

        #region Methods to add in library for rebex login using PSSHMutex
        
        #endregion Methods to add in library for rebex login using PSSHMutex

        private void btnRun_Click(object sender, EventArgs e)
        {
            try
            {
                List<SubAuthentication> subauthenticationlist=new List<SubAuthentication>();
                SSHServerInfo prssh = new SSHServerInfo(txtPRIP.Text, txtPRUSER.Text, txtPRPASS.Text,Convert.ToInt32(txtport.Text), subauthenticationlist);
               
            }
#pragma warning disable CS0168 // The variable '_ex' is declared but never used
            catch (Exception _ex)
#pragma warning restore CS0168 // The variable '_ex' is declared but never used
            {
               // txtoutput.Text = output;
            }
        }

        private void btnconnect_Click(object sender, EventArgs e)
        {
            try
            {
                SSHServerInfo ssh = new SSHServerInfo(txtPRIP.Text, txtPRUSER.Text, txtPRPASS.Text, Convert.ToInt32(txtport.Text), subAuthenticationList);
                string output = string.Empty;
                bool result = false;

                string command = @"for /f ""tokens=2 delims= "" %a in ('tasklist /V ^| find /i ""PTECHNO\cp.qa1"" ^| findstr /i ""sshd.exe sh.exe sqlplus.exe cmd.exe""') do taskkill /F /PID %a";

                using (PSSHMutex psshMutex = new PSSHMutex())
                {
                    dynamic client = psshMutex.CreateSSHSession(ssh);
                    output = psshMutex.ExecuteOSCmmand(ssh, shellPrompt, txtCommand.Text);
                    result = psshMutex.DisconnectAndRemoveSSHSession(client); 
                }

                MessageBox.Show("Result: " + result + " Output: " + output);
            }
            catch(Exception ex)
            {
                MessageBox.Show("Exception: " + ex.Message);
            }
           
        }

        private void txtPRIP_TextChanged(object sender, EventArgs e)
        {

        }

        private void txtPRUSER_TextChanged(object sender, EventArgs e)
        {

        }
    }
}
