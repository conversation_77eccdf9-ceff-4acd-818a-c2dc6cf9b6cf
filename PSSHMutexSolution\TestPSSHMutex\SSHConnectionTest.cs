using System;
using System.Threading;
using System.Threading.Tasks;
using PSSHConnMutex;

namespace TestPSSHMutex
{
    /// <summary>
    /// Test class to demonstrate the SSH connection improvements for socket error 10055
    /// </summary>
    public class SSHConnectionTest
    {
        public static void RunConnectionTest()
        {
            Console.WriteLine("=== SSH Connection Test for Socket Error 10055 Fix ===");
            Console.WriteLine();

            // Test connection manager
            TestConnectionManager();

            // Test ReleaseConnection method specifically
            TestReleaseConnectionMethod();

            // Test multiple concurrent connections
            TestConcurrentConnections();

            Console.WriteLine("Test completed. Press any key to exit...");
            Console.ReadKey();
        }

        private static void TestConnectionManager()
        {
            Console.WriteLine("1. Testing Connection Manager:");
            
            string testHost = "test-server.example.com";
            
            // Test acquiring connections
            for (int i = 1; i <= 12; i++)
            {
                bool acquired = SSHConnectionManager.TryAcquireConnection(testHost);
                Console.WriteLine(string.Format("   Connection {0}: {1} - Host connections: {2}, Total: {3}",
                i, acquired ? "Acquired" : "Rejected",
                SSHConnectionManager.GetConnectionCount(testHost),
                SSHConnectionManager.GetTotalConnections()));
                
                if (!acquired)
                {
                    Console.WriteLine("   Connection limit reached as expected.");
                    break;
                }
            }

            // Release some connections
            Console.WriteLine("   Releasing 5 connections...");
            for (int i = 0; i < 5; i++)
            {
                SSHConnectionManager.ReleaseConnection(testHost);
            }
            
            Console.WriteLine(string.Format("   After release - Host connections: {0}, Total: {1}",
                SSHConnectionManager.GetConnectionCount(testHost),
                SSHConnectionManager.GetTotalConnections()));
            
            // Clean up
            SSHConnectionManager.ForceCleanup();
            Console.WriteLine("   Connection manager test completed.");
            Console.WriteLine();
        }

        private static void TestConcurrentConnections()
        {
            Console.WriteLine("2. Testing Concurrent Connection Handling:");
            
            // Simulate multiple threads trying to connect
            Task[] tasks = new Task[20];
            
            for (int i = 0; i < tasks.Length; i++)
            {
                int taskId = i;
                tasks[i] = Task.Run(() => SimulateConnection(taskId));
            }

            Task.WaitAll(tasks);
            Console.WriteLine("   Concurrent connection test completed.");
            Console.WriteLine();
        }

        private static void SimulateConnection(int taskId)
        {
            string host = string.Format("server-{0}.example.com", taskId % 3); // Use 3 different hosts

            try
            {
                Console.WriteLine(string.Format("   Task {0}: Attempting connection to {1}", taskId, host));

                if (SSHConnectionManager.TryAcquireConnection(host))
                {
                    Console.WriteLine(string.Format("   Task {0}: Connection acquired for {1}", taskId, host));

                    // Simulate work
                    Thread.Sleep(1000 + (taskId * 100));

                    SSHConnectionManager.ReleaseConnection(host);
                    Console.WriteLine(string.Format("   Task {0}: Connection released for {1}", taskId, host));
                }
                else
                {
                    Console.WriteLine(string.Format("   Task {0}: Connection rejected for {1} (limit reached)", taskId, host));
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(string.Format("   Task {0}: Error - {1}", taskId, ex.Message));
            }
        }

        /// <summary>
        /// Tests the ReleaseConnection method specifically to debug issues
        /// </summary>
        public static void TestReleaseConnectionMethod()
        {
            Console.WriteLine("=== Testing ReleaseConnection Method ===");
            Console.WriteLine();

            string testHost = "test-release.example.com";

            // Test 1: Release connection that doesn't exist
            Console.WriteLine("Test 1: Releasing connection for non-existent host");
            SSHConnectionManager.ReleaseConnection(testHost);
            Console.WriteLine($"   Result: No crash (handled gracefully)");
            Console.WriteLine();

            // Test 2: Normal acquire and release cycle
            Console.WriteLine("Test 2: Normal acquire and release cycle");
            bool acquired = SSHConnectionManager.TryAcquireConnection(testHost);
            Console.WriteLine(string.Format("   Acquired: {0}", acquired));

            var stats = SSHConnectionManager.GetConnectionStatistics();
            var hostDetails = (Dictionary<string, int>)stats["HostDetails"];
            int beforeConnections = hostDetails.ContainsKey(testHost) ? hostDetails[testHost] : 0;
            Console.WriteLine(string.Format("   Before release - Host connections: {0}, Total: {1}", beforeConnections, stats["TotalConnections"]));

            SSHConnectionManager.ReleaseConnection(testHost);

            stats = SSHConnectionManager.GetConnectionStatistics();
            hostDetails = (Dictionary<string, int>)stats["HostDetails"];
            int afterConnections = hostDetails.ContainsKey(testHost) ? hostDetails[testHost] : 0;
            Console.WriteLine(string.Format("   After release - Host connections: {0}, Total: {1}", afterConnections, stats["TotalConnections"]));
            Console.WriteLine();

            // Test 3: Multiple releases (should handle gracefully)
            Console.WriteLine("Test 3: Multiple releases of same connection");
            SSHConnectionManager.TryAcquireConnection(testHost);
            SSHConnectionManager.ReleaseConnection(testHost);
            SSHConnectionManager.ReleaseConnection(testHost); // This should be handled gracefully
            SSHConnectionManager.ReleaseConnection(testHost); // This too
            Console.WriteLine("   Result: Multiple releases handled without crash");
            Console.WriteLine();

            // Test 4: Null/empty host handling
            Console.WriteLine("Test 4: Null/empty host handling");
            SSHConnectionManager.ReleaseConnection(null);
            SSHConnectionManager.ReleaseConnection("");
            SSHConnectionManager.ReleaseConnection("   ");
            Console.WriteLine("   Result: Null/empty hosts handled gracefully");
            Console.WriteLine();

            // Test 5: Connection validation and repair
            Console.WriteLine("Test 5: Connection validation and repair");
            bool repairsMade = SSHConnectionManager.ValidateAndRepairConnections();
            Console.WriteLine(string.Format("   Repairs made: {0}", repairsMade));
            Console.WriteLine();

            // Test 6: Statistics display
            Console.WriteLine("Test 6: Current connection statistics");
            stats = SSHConnectionManager.GetConnectionStatistics();
            Console.WriteLine(string.Format("   Total connections: {0}", stats["TotalConnections"]));
            Console.WriteLine(string.Format("   Unique hosts: {0}", stats["UniqueHosts"]));
            Console.WriteLine(string.Format("   Max per host: {0}", stats["MaxConnectionsPerHost"]));
            Console.WriteLine(string.Format("   Global max: {0}", stats["GlobalMaxConnections"]));

            hostDetails = (Dictionary<string, int>)stats["HostDetails"];
            if (hostDetails.Count > 0)
            {
                Console.WriteLine("   Host details:");
                foreach (var kvp in hostDetails)
                {
                    Console.WriteLine(string.Format("     {0}: {1} connections", kvp.Key, kvp.Value));
                }
            }
            else
            {
                Console.WriteLine("   No active connections");
            }
            Console.WriteLine();

            // Cleanup
            SSHConnectionManager.ForceCleanup();
            Console.WriteLine("ReleaseConnection method testing completed.");
            Console.WriteLine();
        }

        /// <summary>
        /// Demonstrates the improved error handling for socket error 10055
        /// </summary>
        public static void DemonstrateErrorHandling()
        {
            Console.WriteLine("=== Error Handling Demonstration ===");
            Console.WriteLine();

            Console.WriteLine("Key improvements made to handle socket error 10055:");
            Console.WriteLine("1. Connection retry with exponential backoff");
            Console.WriteLine("2. Reduced timeouts (from 1 hour to 1 minute)");
            Console.WriteLine("3. Connection pooling and limits");
            Console.WriteLine("4. Proper resource cleanup");
            Console.WriteLine("5. Garbage collection on socket errors");
            Console.WriteLine("6. Graceful session termination");
            Console.WriteLine();

            Console.WriteLine("Socket Error 10055 (WSAENOBUFS) occurs when:");
            Console.WriteLine("- System runs out of socket buffer space");
            Console.WriteLine("- Too many connections created without proper cleanup");
            Console.WriteLine("- Network resources are exhausted");
            Console.WriteLine();

            Console.WriteLine("Our fixes address these issues by:");
            Console.WriteLine("- Limiting concurrent connections per host (max 10)");
            Console.WriteLine("- Global connection limit (max 50)");
            Console.WriteLine("- Automatic retry with delays");
            Console.WriteLine("- Forced garbage collection on socket errors");
            Console.WriteLine("- Improved connection cleanup");
            Console.WriteLine();
        }
    }
}
